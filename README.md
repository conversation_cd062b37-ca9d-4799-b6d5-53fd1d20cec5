# Live2D Material Separation Plugin for macOS

## ����

����Live2D Cubism Material Separation�����macOS�汾��һ������Photoshop����չ�������Զ���Live2D��ɫͼ�����Ϊ��ͬ�Ĳ��ʲ㡣�ð汾����ȫ��Դ�ģ��Ƴ�������֤��֤��������֤��Ӳ��ָ�ư󶨻��ơ�

## �����ص�

- �Զ������ͷ���Live2D��ɫͼ���еĲ�ͬ���ʣ�Ƥ����ͷ�����۾����·��ȣ�
- ���ɷֲ��PSD�ļ�������Live2Dģ������
- ֧��������ϸ�ڼ���ĵ���
- ��ȫ��Դ��������֤��֤
- ���Apple Silicon�Ż���ʹ��Metal��ܼ���

## ϵͳҪ��

- macOS 11.0 (Big Sur) ����߰汾
- Adobe Photoshop 2021 (22.0) ����߰汾
- Python 3.10 ����߰汾

## ��װ����

### �Զ���װ

1. ȷ���Ѱ�װPython 3.10����߰汾
2. ���նˣ����������Ŀ¼
3. ���а�װ�ű���`./install_plugin.sh`
4. ����Photoshop

### �ֶ���װ

1. ȷ���Ѱ�װPython 3.10����߰汾
2. ����`./download_models.sh`���ر�Ҫ��ģ���ļ�
3. ����`./build.sh`������Ŀ�
4. �����ɵĲ���ļ��и��Ƶ�Adobe CEP��չĿ¼��
   `/Library/Application Support/Adobe/CEP/extensions/`
5. ����Photoshop

## ʹ�÷���

1. ��Photoshop�У�ѡ��`���� > ��չ���� > Live2D Material Separation`
2. ѡ��Ҫ������ͼ�񣨿����ǵ�ǰ�ĵ���Ӵ���ѡ��
3. ����������ϸ�ڼ���
4. ѡ��Ҫ��ȡ�Ĳ�������
5. ���"����ͼ��"��ť
6. �ȴ�������ɣ�������Զ�����Ϊ�ֲ��PSD�ļ�

## ����ָ��

### ��Ŀ�ṹ

- `Source/Plugin`: Photoshop CEP��չ�ļ�
- `Source/Core`: ���Ĵ����⣨Objective-C++��
- `Source/Python`: Pythonģ������ѧϰģ�ͽӿ�
- `Models`: Ԥѵ�������ѧϰģ��
- `Build`: �������Ŀ¼

### ������Ŀ

1. ��װ��Ҫ��������
   ```
   pip install -r requirements.txt
   ```

2. ʹ��Makefile������Ŀ⣺
   ```
   cd Source/Core
   make
   ```

3. ��ʹ���ṩ�Ĺ����ű���
   ```
   ./build.sh
   ```

## �����ų�

- ��������Photoshop�в��ɼ�������CEP��չĿ¼Ȩ��
- �������ʧ�ܣ�����Python�����������װ
- �鿴��־�ļ���`~/Library/Logs/CSXS/CEP*.log`

## ����֤

����Ŀ����MIT����֤�����[LICENSE](LICENSE)�ļ���

## ��л

- ����Ŀʹ���˶����Դ��Ϳ�ܣ�����PyTorch��OpenCV��
- ��л����ΪԭʼWindows�汾�������׵Ŀ�����
