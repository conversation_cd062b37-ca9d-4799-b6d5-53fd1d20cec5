/**
 * Live2D Material Separation Plugin for macOS
 * Main JavaScript File
 * 
 * This file handles the UI interactions and communication with Photoshop
 * through the CEP interface. It's responsible for sending commands to
 * the core processing library and displaying results.
 */

// Initialize the CSInterface
var csInterface = new CSInterface();
var extensionPath = csInterface.getSystemPath(SystemPath.EXTENSION);

// Document state
var currentDocumentInfo = null;
var selectedImagePath = null;
var isProcessing = false;

// DOM Elements
var selectImageBtn = document.getElementById('select-image');
var useCurrentBtn = document.getElementById('use-current');
var processBtn = document.getElementById('process-button');
var cancelBtn = document.getElementById('cancel-button');
var previewImage = document.getElementById('preview-image');
var previewPlaceholder = document.getElementById('preview-placeholder');
var progressModal = document.getElementById('progress-modal');
var progressFill = document.getElementById('progress-fill');
var progressText = document.getElementById('progress-text');
var progressStatus = document.getElementById('progress-status');
var statusText = document.getElementById('status');

// Sliders
var qualitySlider = document.getElementById('quality-slider');
var qualityValue = document.getElementById('quality-value');
var detailSlider = document.getElementById('detail-slider');
var detailValue = document.getElementById('detail-value');

// Initialize the plugin
function init() {
    // Register event listeners
    selectImageBtn.addEventListener('click', selectImage);
    useCurrentBtn.addEventListener('click', useCurrentDocument);
    processBtn.addEventListener('click', processImage);
    cancelBtn.addEventListener('click', cancelProcessing);
    
    // Slider event listeners
    qualitySlider.addEventListener('input', updateQualityValue);
    detailSlider.addEventListener('input', updateDetailValue);
    
    // Initialize slider values
    updateQualityValue();
    updateDetailValue();
    
    // Check if Photoshop is available
    checkPhotoshopConnection();
    
    // Register Photoshop event listeners
    registerPhotoshopEventListeners();
    
    // Load the core library
    loadCoreLibrary();
}

// Check if we can connect to Photoshop
function checkPhotoshopConnection() {
    csInterface.evalScript('app.name', function(result) {
        if (result && result.indexOf('Photoshop') !== -1) {
            statusText.textContent = 'Connected to ' + result;
            useCurrentBtn.disabled = false;
        } else {
            statusText.textContent = 'Could not connect to Photoshop';
            useCurrentBtn.disabled = true;
        }
    });
}

// Register event listeners for Photoshop events
function registerPhotoshopEventListeners() {
    csInterface.addEventListener('documentAfterActivate', function() {
        updateCurrentDocumentInfo();
    });
    
    // Update on initial load
    updateCurrentDocumentInfo();
}

// Update information about the current document
function updateCurrentDocumentInfo() {
    csInterface.evalScript(
        'app.documents.length > 0 ? JSON.stringify({name: app.activeDocument.name, width: app.activeDocument.width.value, height: app.activeDocument.height.value}) : "null"',
        function(result) {
            try {
                currentDocumentInfo = JSON.parse(result);
                if (currentDocumentInfo) {
                    useCurrentBtn.textContent = 'Use "' + currentDocumentInfo.name + '"';
                    useCurrentBtn.disabled = false;
                } else {
                    useCurrentBtn.textContent = 'Use Current Document';
                    useCurrentBtn.disabled = true;
                }
            } catch (e) {
                console.error('Error parsing document info:', e);
                currentDocumentInfo = null;
                useCurrentBtn.textContent = 'Use Current Document';
                useCurrentBtn.disabled = true;
            }
        }
    );
}

// Select an image file from disk
function selectImage() {
    var script = 'File.openDialog("Select an image", "*.jpg;*.jpeg;*.png;*.tif;*.tiff;*.psd")'; 
    
    csInterface.evalScript(script, function(result) {
        if (result && result !== 'null') {
            selectedImagePath = result.replace(/^file:\/\//, '');
            
            // Display the selected image
            previewPlaceholder.style.display = 'none';
            previewImage.src = 'file://' + selectedImagePath;
            previewImage.style.display = 'block';
            
            statusText.textContent = 'Image selected: ' + selectedImagePath.split('/').pop();
        }
    });
}

// Use the current active document in Photoshop
function useCurrentDocument() {
    if (!currentDocumentInfo) {
        alert('No active document in Photoshop');
        return;
    }
    
    // Get a temporary path for the current document
    csInterface.evalScript(
        'var tempPath = Folder.temp.fsName + "/temp_live2d_material_separation.jpg"; ' +
        'app.activeDocument.saveAs(new File(tempPath), new JPEGSaveOptions(), true); ' +
        'tempPath;',
        function(result) {
            if (result && result !== 'null') {
                selectedImagePath = result;
                
                // Display the selected image
                previewPlaceholder.style.display = 'none';
                previewImage.src = 'file://' + selectedImagePath;
                previewImage.style.display = 'block';
                
                statusText.textContent = 'Using current document: ' + currentDocumentInfo.name;
            }
        }
    );
}

// Update the quality value display
function updateQualityValue() {
    qualityValue.textContent = qualitySlider.value + '%';
}

// Update the detail value display
function updateDetailValue() {
    detailValue.textContent = detailSlider.value + '%';
}

// Load the core processing library
function loadCoreLibrary() {
    // In a real implementation, this would load the dylib
    // For this demo, we'll simulate the loading
    setTimeout(function() {
        statusText.textContent = 'Core library loaded successfully';
    }, 1000);
}

// Process the selected image
function processImage() {
    if (!selectedImagePath) {
        alert('Please select an image first');
        return;
    }
    
    if (isProcessing) {
        alert('Processing is already in progress');
        return;
    }
    
    // Get settings
    var settings = {
        quality: parseInt(qualitySlider.value),
        detailLevel: parseInt(detailSlider.value),
        layerMode: document.getElementById('layer-mode').value,
        materials: {
            skin: document.getElementById('material-skin').checked,
            hair: document.getElementById('material-hair').checked,
            eyes: document.getElementById('material-eyes').checked,
            clothes: document.getElementById('material-clothes').checked,
            accessories: document.getElementById('material-accessories').checked,
            background: document.getElementById('material-background').checked
        }
    };
    
    // Show the progress modal
    progressFill.style.width = '0%';
    progressText.textContent = '0%';
    progressStatus.textContent = 'Initializing...';
    progressModal.style.display = 'flex';
    
    isProcessing = true;
    
    // In a real implementation, this would call the core library
    // For this demo, we'll simulate the processing
    simulateProcessing(settings);
}

// Simulate the processing of the image
function simulateProcessing(settings) {
    var progress = 0;
    var processingSteps = [
        'Loading models...',
        'Analyzing image...',
        'Detecting materials...',
        'Separating skin...',
        'Separating hair...',
        'Separating eyes...',
        'Separating clothes...',
        'Separating accessories...',
        'Separating background...',
        'Finalizing results...'
    ];
    
    var processingInterval = setInterval(function() {
        if (!isProcessing) {
            clearInterval(processingInterval);
            return;
        }
        
        progress += 1;
        var progressPercent = Math.min(Math.round((progress / 100) * 100), 100);
        
        progressFill.style.width = progressPercent + '%';
        progressText.textContent = progressPercent + '%';
        
        // Update status message based on progress
        var stepIndex = Math.min(Math.floor(progress / 10), processingSteps.length - 1);
        progressStatus.textContent = processingSteps[stepIndex];
        
        if (progress >= 100) {
            clearInterval(processingInterval);
            finishProcessing(settings);
        }
    }, 100);
}

// Finish the processing and create the result in Photoshop
function finishProcessing(settings) {
    progressStatus.textContent = 'Creating layers in Photoshop...';
    
    // Create a script to generate layers in Photoshop
    var jsxScript = 'createMaterialLayers("' + selectedImagePath + '", ' + JSON.stringify(settings) + ')';
    
    csInterface.evalScript(jsxScript, function(result) {
        isProcessing = false;
        progressModal.style.display = 'none';
        
        if (result === 'success') {
            statusText.textContent = 'Material separation completed successfully';
        } else {
            statusText.textContent = 'Error: ' + result;
        }
    });
}

// Cancel the processing
function cancelProcessing() {
    if (isProcessing) {
        isProcessing = false;
        progressModal.style.display = 'none';
        statusText.textContent = 'Processing cancelled';
        
        // In a real implementation, this would call the core library to cancel the processing
        // For this demo, we'll just update the UI
    }
}

// Initialize the plugin when the DOM is ready
document.addEventListener('DOMContentLoaded', init);
