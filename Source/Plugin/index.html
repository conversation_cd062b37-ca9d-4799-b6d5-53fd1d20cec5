<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Live2D Material Separation</title>
    <link rel="stylesheet" href="./css/style.css">
    <!-- Include Adobe CEP libraries -->
    <script src="./lib/CSInterface.js"></script>
    <script src="./lib/jquery-3.6.0.min.js"></script>
</head>
<body>
    <div class="container">
        <header>
            <h1>Live2D Material Separation</h1>
            <p class="version">v1.0.0 - Open Source Edition</p>
        </header>

        <main>
            <section class="image-section">
                <h2>Image</h2>
                <div class="image-preview">
                    <div id="preview-placeholder">No image selected</div>
                    <img id="preview-image" style="display: none;" />
                </div>
                <div class="button-group">
                    <button id="select-image" class="primary-button">Select Image</button>
                    <button id="use-current" class="secondary-button">Use Current Document</button>
                </div>
            </section>

            <section class="settings-section">
                <h2>Settings</h2>
                
                <div class="setting-item">
                    <label for="quality-slider">Quality</label>
                    <div class="slider-container">
                        <input type="range" id="quality-slider" min="0" max="100" value="50" class="slider">
                        <span id="quality-value">50%</span>
                    </div>
                    <p class="setting-description">Higher quality produces cleaner results but takes longer</p>
                </div>
                
                <div class="setting-item">
                    <label for="detail-slider">Detail Level</label>
                    <div class="slider-container">
                        <input type="range" id="detail-slider" min="0" max="100" value="50" class="slider">
                        <span id="detail-value">50%</span>
                    </div>
                    <p class="setting-description">Higher detail preserves more fine details in the separation</p>
                </div>
                
                <div class="setting-item">
                    <label for="layer-mode">Layer Mode</label>
                    <select id="layer-mode" class="dropdown">
                        <option value="separate">Separate Layers</option>
                        <option value="group">Layer Group</option>
                        <option value="smart">Smart Object</option>
                    </select>
                </div>
            </section>

            <section class="materials-section">
                <h2>Materials to Extract</h2>
                <div class="materials-grid">
                    <div class="material-item">
                        <input type="checkbox" id="material-skin" checked>
                        <label for="material-skin">Skin</label>
                    </div>
                    <div class="material-item">
                        <input type="checkbox" id="material-hair" checked>
                        <label for="material-hair">Hair</label>
                    </div>
                    <div class="material-item">
                        <input type="checkbox" id="material-eyes" checked>
                        <label for="material-eyes">Eyes</label>
                    </div>
                    <div class="material-item">
                        <input type="checkbox" id="material-clothes" checked>
                        <label for="material-clothes">Clothes</label>
                    </div>
                    <div class="material-item">
                        <input type="checkbox" id="material-accessories" checked>
                        <label for="material-accessories">Accessories</label>
                    </div>
                    <div class="material-item">
                        <input type="checkbox" id="material-background" checked>
                        <label for="material-background">Background</label>
                    </div>
                </div>
            </section>
        </main>

        <footer>
            <button id="process-button" class="primary-button">Process Image</button>
            <div id="status" class="status-text"></div>
        </footer>

        <!-- Progress Modal -->
        <div id="progress-modal" class="modal">
            <div class="modal-content">
                <h2>Processing Image</h2>
                <div class="progress-container">
                    <div id="progress-bar" class="progress-bar">
                        <div id="progress-fill" class="progress-fill"></div>
                    </div>
                    <div id="progress-text" class="progress-text">0%</div>
                </div>
                <p id="progress-status">Initializing...</p>
                <button id="cancel-button" class="secondary-button">Cancel</button>
            </div>
        </div>
    </div>

    <script src="./js/main.js"></script>
</body>
</html>
