/*************************************************************************
* ADOBE CONFIDENTIAL
* ___________________
*
* Copyright 2014 Adobe Inc.
* All Rights Reserved.
*
* NOTICE: Adobe permits you to use, modify, and distribute this file in
* accordance with the terms of the Adobe license agreement accompanying
* it. If you have received this file from a source other than Adobe,
* then your use, modification, or distribution of it requires the prior
* written permission of Adobe. 
**************************************************************************/

/**
 * Stub implementation of CSInterface for the Live2D Material Separation Plugin
 * This is a simplified version of Adobe's CSInterface.js for macOS compatibility
 */

/**
 * @class CSInterface
 * This is the entry point to the CEP extensibility infrastructure.
 * Instantiate this object and use it to:
 * <ul>
 * <li>Access information about the host application in which an extension is running</li>
 * <li>Launch an extension</li>
 * <li>Register interest in event notifications, and dispatch events</li>
 * </ul>
 *
 * @param {Object} options Optional parameters, currently only supports:
 *        defaultCallback: a callback function for unsolicited events from the host application.
 */
function CSInterface(options) {
    this.options = options || {};
    this.defaultCallback = this.options.defaultCallback || function() {};
    this.eventListeners = {};
}

/**
 * Retrieves information about the host environment in which the extension is currently running.
 *
 * @return {HostEnvironment} A HostEnvironment object.
 */
CSInterface.prototype.getHostEnvironment = function() {
    return {
        appName: "PHSP", // Photoshop
        appVersion: "23.0.0",
        appLocale: "en_US",
        appUILocale: "en_US",
        appId: "PHSP",
        isAppOnline: true,
        appSkinInfo: {
            baseFontFamily: "Lucida Grande",
            baseFontSize: 12,
            appBarBackgroundColor: {red: 52, green: 52, blue: 52},
            panelBackgroundColor: {red: 52, green: 52, blue: 52},
            appBarBackgroundColorSRGB: "#343434",
            panelBackgroundColorSRGB: "#343434",
            systemHighlightColor: {red: 204, green: 204, blue: 255}
        }
    };
};

/**
 * Retrieves the path of the extension.
 *
 * @return {String} The path of the extension.
 */
CSInterface.prototype.getSystemPath = function(pathType) {
    var path = "";
    
    switch (pathType) {
        case SystemPath.EXTENSION:
            path = "/Library/Application Support/Adobe/CEP/extensions/Live2D-MaterialSeparation";
            break;
        case SystemPath.USER_DATA:
            path = "/Users/<USER>/Library/Application Support/Adobe/CEP/extensions/Live2D-MaterialSeparation/userData";
            break;
        case SystemPath.COMMON_FILES:
            path = "/Library/Application Support/Adobe/CEP/extensions/Live2D-MaterialSeparation/commonFiles";
            break;
        case SystemPath.MY_DOCUMENTS:
            path = "/Users/<USER>/Documents";
            break;
        case SystemPath.APPLICATION:
            path = "/Applications/Adobe Photoshop 2022/Adobe Photoshop 2022.app";
            break;
        default:
            path = "";
            break;
    }
    
    return path;
};

/**
 * Evaluates a JavaScript script in the host application.
 *
 * @param script The JavaScript script.
 * @param callback Optional. A callback function that receives the result of execution.
 *        If execution fails, the callback function receives the error message as a parameter.
 */
CSInterface.prototype.evalScript = function(script, callback) {
    // In a real implementation, this would communicate with the host application
    // For this demo, we'll simulate some basic functionality
    
    setTimeout(function() {
        if (callback) {
            // Simulate some basic responses
            if (script.indexOf('app.name') !== -1) {
                callback('Adobe Photoshop');
            } else if (script.indexOf('File.openDialog') !== -1) {
                callback('/Users/<USER>/Desktop/sample_image.png');
            } else if (script.indexOf('app.documents.length') !== -1) {
                callback('{"name":"Document.psd","width":1000,"height":1000}');
            } else if (script.indexOf('createMaterialLayers') !== -1) {
                callback('success');
            } else {
                callback('null');
            }
        }
    }, 100);
};

/**
 * Registers an event listener for the specified event type.
 *
 * @param type The event type.
 * @param listener The event listener.
 * @param obj Optional, the object that contains the event listener method.
 */
CSInterface.prototype.addEventListener = function(type, listener, obj) {
    if (!this.eventListeners[type]) {
        this.eventListeners[type] = [];
    }
    
    this.eventListeners[type].push({
        listener: listener,
        obj: obj
    });
};

/**
 * Removes an event listener for the specified event type.
 *
 * @param type The event type.
 * @param listener The event listener.
 * @param obj Optional, the object that contains the event listener method.
 */
CSInterface.prototype.removeEventListener = function(type, listener, obj) {
    if (!this.eventListeners[type]) {
        return;
    }
    
    var newArray = [];
    for (var i = 0; i < this.eventListeners[type].length; i++) {
        var listenerInfo = this.eventListeners[type][i];
        if (listenerInfo.listener !== listener || listenerInfo.obj !== obj) {
            newArray.push(listenerInfo);
        }
    }
    
    this.eventListeners[type] = newArray;
};

/**
 * Dispatches an event to all registered listeners.
 *
 * @param event The event object to dispatch.
 */
CSInterface.prototype.dispatchEvent = function(event) {
    if (!this.eventListeners[event.type]) {
        return;
    }
    
    for (var i = 0; i < this.eventListeners[event.type].length; i++) {
        var listenerInfo = this.eventListeners[event.type][i];
        var context = listenerInfo.obj || null;
        listenerInfo.listener.call(context, event);
    }
};

/**
 * Constants for the system paths.
 */
var SystemPath = {
    EXTENSION: 0,
    USER_DATA: 1,
    COMMON_FILES: 2,
    MY_DOCUMENTS: 3,
    APPLICATION: 4
};
