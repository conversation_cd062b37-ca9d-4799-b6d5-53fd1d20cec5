// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 54;
	objects = {

/* Begin PBXBuildFile section */
		8A1B2C3D4E5F6A7B8C9D0E1F /* MaterialSeparationCore.mm in Sources */ = {isa = PBXBuildFile; fileRef = 8A1B2C3D4E5F6A7B8C9D0E2F /* MaterialSeparationCore.mm */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		8A1B2C3D4E5F6A7B8C9D0E0F /* MaterialSeparationCore.dylib */ = {isa = PBXFileReference; explicitFileType = "compiled.mach-o.dylib"; includeInIndex = 0; path = MaterialSeparationCore.dylib; sourceTree = BUILT_PRODUCTS_DIR; };
		8A1B2C3D4E5F6A7B8C9D0E2F /* MaterialSeparationCore.mm */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.cpp.objcpp; path = MaterialSeparationCore.mm; sourceTree = "<group>"; };
		8A1B2C3D4E5F6A7B8C9D0E3F /* Makefile */ = {isa = PBXFileReference; lastKnownFileType = sourcecode.make; path = Makefile; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFrameworksBuildPhase section */
		8A1B2C3D4E5F6A7B8C9D0E4F /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		8A1B2C3D4E5F6A7B8C9D0E5F = {
			isa = PBXGroup;
			children = (
				8A1B2C3D4E5F6A7B8C9D0E6F,
				8A1B2C3D4E5F6A7B8C9D0E7F,
			);
			sourceTree = "<group>";
		};
		8A1B2C3D4E5F6A7B8C9D0E6F /* Products */ = {
			isa = PBXGroup;
			children = (
				8A1B2C3D4E5F6A7B8C9D0E0F,
			);
			name = Products;
			sourceTree = "<group>";
		};
		8A1B2C3D4E5F6A7B8C9D0E7F /* Source */ = {
			isa = PBXGroup;
			children = (
				8A1B2C3D4E5F6A7B8C9D0E2F,
				8A1B2C3D4E5F6A7B8C9D0E3F,
			);
			name = Source;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXHeadersBuildPhase section */
		8A1B2C3D4E5F6A7B8C9D0E8F /* Headers */ = {
			isa = PBXHeadersBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXHeadersBuildPhase section */

/* Begin PBXNativeTarget section */
		8A1B2C3D4E5F6A7B8C9D0E9F /* MaterialSeparationCore */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 8A1B2C3D4E5F6A7B8C9D0EAF;
			buildPhases = (
				8A1B2C3D4E5F6A7B8C9D0E8F /* Headers */,
				8A1B2C3D4E5F6A7B8C9D0EBF /* Sources */,
				8A1B2C3D4E5F6A7B8C9D0E4F /* Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			name = MaterialSeparationCore;
			productName = MaterialSeparationCore;
			productReference = 8A1B2C3D4E5F6A7B8C9D0E0F;
			productType = "com.apple.product-type.library.dynamic";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		8A1B2C3D4E5F6A7B8C9D0ECF /* Project object */ = {
			isa = PBXProject;
			attributes = {
				LastUpgradeCheck = 1250;
				ORGANIZATIONNAME = "Live2D";
				TargetAttributes = {
					8A1B2C3D4E5F6A7B8C9D0E9F = {
						CreatedOnToolsVersion = 12.5;
					};
				};
			};
			buildConfigurationList = 8A1B2C3D4E5F6A7B8C9D0EDF;
			compatibilityVersion = "Xcode 12.0";
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 8A1B2C3D4E5F6A7B8C9D0E5F;
			productRefGroup = 8A1B2C3D4E5F6A7B8C9D0E6F;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				8A1B2C3D4E5F6A7B8C9D0E9F,
			);
		};
/* End PBXProject section */

/* Begin PBXSourcesBuildPhase section */
		8A1B2C3D4E5F6A7B8C9D0EBF /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
				8A1B2C3D4E5F6A7B8C9D0E1F /* MaterialSeparationCore.mm in Sources */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		8A1B2C3D4E5F6A7B8C9D0EEF /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = macosx;
			};
			name = Debug;
		};
		8A1B2C3D4E5F6A7B8C9D0EFF /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "c++17";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				GCC_C_LANGUAGE_STANDARD = gnu11;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				MACOSX_DEPLOYMENT_TARGET = 11.0;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = macosx;
			};
			name = Release;
		};
		8A1B2C3D4E5F6A7B8C9D0F0F /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				EXECUTABLE_PREFIX = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(SYSTEM_LIBRARY_DIR)/Frameworks",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"/Library/Frameworks/Python.framework/Versions/3.10/include/python3.10",
				);
				INSTALL_PATH = "@executable_path/../Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"/Library/Frameworks/Python.framework/Versions/3.10/lib",
				);
				OTHER_LDFLAGS = (
					"-framework",
					"Foundation",
					"-framework",
					"Metal",
					"-framework",
					"MetalPerformanceShaders",
					"-lpython3.10",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Debug;
		};
		8A1B2C3D4E5F6A7B8C9D0F1F /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				DYLIB_COMPATIBILITY_VERSION = 1;
				DYLIB_CURRENT_VERSION = 1;
				EXECUTABLE_PREFIX = "";
				FRAMEWORK_SEARCH_PATHS = (
					"$(inherited)",
					"$(SYSTEM_LIBRARY_DIR)/Frameworks",
				);
				HEADER_SEARCH_PATHS = (
					"$(inherited)",
					"/Library/Frameworks/Python.framework/Versions/3.10/include/python3.10",
				);
				INSTALL_PATH = "@executable_path/../Frameworks";
				LIBRARY_SEARCH_PATHS = (
					"$(inherited)",
					"/Library/Frameworks/Python.framework/Versions/3.10/lib",
				);
				OTHER_LDFLAGS = (
					"-framework",
					"Foundation",
					"-framework",
					"Metal",
					"-framework",
					"MetalPerformanceShaders",
					"-lpython3.10",
				);
				PRODUCT_NAME = "$(TARGET_NAME)";
				SKIP_INSTALL = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		8A1B2C3D4E5F6A7B8C9D0EAF /* Build configuration list for PBXNativeTarget "MaterialSeparationCore" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8A1B2C3D4E5F6A7B8C9D0F0F /* Debug */,
				8A1B2C3D4E5F6A7B8C9D0F1F /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		8A1B2C3D4E5F6A7B8C9D0EDF /* Build configuration list for PBXProject "MaterialSeparationCore" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				8A1B2C3D4E5F6A7B8C9D0EEF /* Debug */,
				8A1B2C3D4E5F6A7B8C9D0EFF /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */
	};
	rootObject = 8A1B2C3D4E5F6A7B8C9D0ECF /* Project object */;
}
