# Makefile for Live2D Material Separation Core Library (macOS)

# Compiler and flags
CXX = clang++
OBJC = clang
CFLAGS = -Wall -Werror -std=c++17 -fobjc-arc -fmodules -O3
LDFLAGS = -framework Foundation -framework Metal -framework MetalPerformanceShaders

# Python configuration
PYTHON_VERSION = 3.10
PYTHON_CONFIG = python$(PYTHON_VERSION)-config
PYTHON_INCLUDES = $(shell $(PYTHON_CONFIG) --includes)
PYTHON_LDFLAGS = $(shell $(PYTHON_CONFIG) --ldflags)

# Directories
SRC_DIR = .
BUILD_DIR = ../../Build
OBJ_DIR = $(BUILD_DIR)/obj
LIB_DIR = $(BUILD_DIR)/lib

# Target library
TARGET = $(LIB_DIR)/MaterialSeparationCore.dylib

# Source files
SRCS = $(wildcard $(SRC_DIR)/*.mm)
OBJS = $(patsubst $(SRC_DIR)/%.mm,$(OBJ_DIR)/%.o,$(SRCS))

# Default target
all: directories $(TARGET)

# Create necessary directories
directories:
	@mkdir -p $(OBJ_DIR)
	@mkdir -p $(LIB_DIR)

# Compile source files
$(OBJ_DIR)/%.o: $(SRC_DIR)/%.mm
	@echo "Compiling $<..."
	@$(CXX) $(CFLAGS) $(PYTHON_INCLUDES) -c $< -o $@

# Link the library
$(TARGET): $(OBJS)
	@echo "Linking $(TARGET)..."
	@$(CXX) -dynamiclib -o $(TARGET) $(OBJS) $(LDFLAGS) $(PYTHON_LDFLAGS)
	@install_name_tool -id @executable_path/../Frameworks/MaterialSeparationCore.dylib $(TARGET)
	@echo "Build complete: $(TARGET)"

# Clean build files
clean:
	@echo "Cleaning build files..."
	@rm -rf $(OBJ_DIR) $(TARGET)

# Install the library (for development)
install: $(TARGET)
	@echo "Installing library to /Library/Application Support/Adobe/CEP/extensions/Live2D-MaterialSeparation/lib/"
	@mkdir -p "/Library/Application Support/Adobe/CEP/extensions/Live2D-MaterialSeparation/lib/"
	@cp $(TARGET) "/Library/Application Support/Adobe/CEP/extensions/Live2D-MaterialSeparation/lib/"

# Debug build
debug: CFLAGS += -g -DDEBUG
debug: clean all

.PHONY: all directories clean install debug
